"""
DTC (Diagnostic Trouble Code) Parser
Handles interpretation and categorization of DTCs
"""
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

from ..config import OBDConfig


logger = logging.getLogger(__name__)


@dataclass
class DTCInfo:
    """Detailed DTC information"""
    code: str
    description: str
    category: str
    severity: str
    system: str
    possible_causes: List[str]
    repair_hints: List[str]
    related_pids: List[str]
    freeze_frame_required: bool = False


class DTCParser:
    """
    DTC Parser for interpreting diagnostic trouble codes
    """
    
    def __init__(self, dtc_database_path: Optional[str] = None):
        self.dtc_database_path = dtc_database_path or "app/data/dtc_codes.json"
        self.dtc_database: Dict[str, Any] = {}
        self.load_dtc_database()
    
    def load_dtc_database(self):
        """
        Load DTC database from JSON file
        """
        try:
            database_path = Path(self.dtc_database_path)
            if database_path.exists():
                with open(database_path, 'r', encoding='utf-8') as f:
                    self.dtc_database = json.load(f)
                logger.info(f"Loaded {len(self.dtc_database)} DTC definitions")
            else:
                logger.warning(f"DTC database not found at {database_path}")
                self._create_default_database()
        except Exception as e:
            logger.error(f"Error loading DTC database: {e}")
            self._create_default_database()
    
    def _create_default_database(self):
        """
        Create a default DTC database with common codes
        """
        self.dtc_database = {
            "P0000": {
                "description": "No fault detected",
                "category": "Powertrain",
                "severity": "Info",
                "system": "General",
                "possible_causes": ["System normal"],
                "repair_hints": ["No action required"],
                "related_pids": []
            },
            "P0100": {
                "description": "Mass Air Flow Circuit Malfunction",
                "category": "Powertrain",
                "severity": "Medium",
                "system": "Fuel and Air Metering",
                "possible_causes": [
                    "Faulty MAF sensor",
                    "Dirty air filter",
                    "Vacuum leak",
                    "Wiring issues"
                ],
                "repair_hints": [
                    "Clean MAF sensor",
                    "Replace air filter",
                    "Check for vacuum leaks",
                    "Inspect wiring harness"
                ],
                "related_pids": ["0x10"]
            },
            "P0171": {
                "description": "System Too Lean (Bank 1)",
                "category": "Powertrain",
                "severity": "Medium",
                "system": "Fuel and Air Metering",
                "possible_causes": [
                    "Vacuum leak",
                    "Faulty fuel pump",
                    "Clogged fuel filter",
                    "Faulty oxygen sensor",
                    "Faulty MAF sensor"
                ],
                "repair_hints": [
                    "Check for vacuum leaks",
                    "Test fuel pressure",
                    "Replace fuel filter",
                    "Test oxygen sensors",
                    "Clean MAF sensor"
                ],
                "related_pids": ["0x06", "0x07", "0x10", "0x14"]
            },
            "P0300": {
                "description": "Random/Multiple Cylinder Misfire Detected",
                "category": "Powertrain",
                "severity": "High",
                "system": "Ignition System",
                "possible_causes": [
                    "Faulty spark plugs",
                    "Faulty ignition coils",
                    "Fuel delivery issues",
                    "Compression problems",
                    "Vacuum leaks"
                ],
                "repair_hints": [
                    "Replace spark plugs",
                    "Test ignition coils",
                    "Check fuel pressure",
                    "Perform compression test",
                    "Check for vacuum leaks"
                ],
                "related_pids": ["0x0C", "0x04"]
            },
            "P0420": {
                "description": "Catalyst System Efficiency Below Threshold (Bank 1)",
                "category": "Powertrain",
                "severity": "Medium",
                "system": "Emission Control",
                "possible_causes": [
                    "Faulty catalytic converter",
                    "Faulty oxygen sensors",
                    "Engine misfire",
                    "Fuel system issues"
                ],
                "repair_hints": [
                    "Replace catalytic converter",
                    "Test oxygen sensors",
                    "Fix any misfires",
                    "Check fuel system"
                ],
                "related_pids": ["0x14", "0x15"]
            }
        }
    
    def parse_dtc(self, dtc_code: str) -> DTCInfo:
        """
        Parse a single DTC code and return detailed information
        """
        # Normalize DTC code
        dtc_code = dtc_code.upper().strip()
        
        # Get basic info from code structure
        category = self._get_dtc_category(dtc_code)
        system = self._get_dtc_system(dtc_code)
        
        # Look up in database
        if dtc_code in self.dtc_database:
            dtc_data = self.dtc_database[dtc_code]
            return DTCInfo(
                code=dtc_code,
                description=dtc_data.get("description", "Unknown DTC"),
                category=dtc_data.get("category", category),
                severity=dtc_data.get("severity", "Unknown"),
                system=dtc_data.get("system", system),
                possible_causes=dtc_data.get("possible_causes", []),
                repair_hints=dtc_data.get("repair_hints", []),
                related_pids=dtc_data.get("related_pids", []),
                freeze_frame_required=dtc_data.get("freeze_frame_required", False)
            )
        else:
            # Return basic info for unknown DTCs
            return DTCInfo(
                code=dtc_code,
                description=f"Unknown DTC: {dtc_code}",
                category=category,
                severity="Unknown",
                system=system,
                possible_causes=["Unknown - requires further diagnosis"],
                repair_hints=["Consult service manual or professional technician"],
                related_pids=[]
            )
    
    def _get_dtc_category(self, dtc_code: str) -> str:
        """
        Determine DTC category from the first character
        """
        if len(dtc_code) > 0:
            prefix = dtc_code[0].upper()
            return OBDConfig.DTC_PREFIXES.get(prefix, "Unknown")
        return "Unknown"
    
    def _get_dtc_system(self, dtc_code: str) -> str:
        """
        Determine system from DTC code structure
        """
        if len(dtc_code) >= 4:
            try:
                # Extract system code (second and third characters)
                system_code = dtc_code[1:3]
                
                # Powertrain system mapping
                if dtc_code.startswith('P'):
                    powertrain_systems = {
                        '00': 'Fuel and Air Metering',
                        '01': 'Fuel and Air Metering',
                        '02': 'Fuel Injector Circuit',
                        '03': 'Ignition System',
                        '04': 'Auxiliary Emission Control',
                        '05': 'Vehicle Speed Control',
                        '06': 'Computer and Auxiliary Outputs',
                        '07': 'Transmission',
                        '08': 'Transmission',
                        '09': 'SAE Reserved',
                        '10': 'SAE Reserved'
                    }
                    return powertrain_systems.get(system_code, "Powertrain - Other")
                
                # Body system mapping
                elif dtc_code.startswith('B'):
                    return "Body System"
                
                # Chassis system mapping
                elif dtc_code.startswith('C'):
                    return "Chassis System"
                
                # Network system mapping
                elif dtc_code.startswith('U'):
                    return "Network/Communication"
                    
            except:
                pass
        
        return "Unknown System"
    
    def parse_multiple_dtcs(self, dtc_codes: List[str]) -> List[DTCInfo]:
        """
        Parse multiple DTC codes
        """
        return [self.parse_dtc(code) for code in dtc_codes]
    
    def categorize_dtcs(self, dtc_infos: List[DTCInfo]) -> Dict[str, List[DTCInfo]]:
        """
        Categorize DTCs by severity and system
        """
        categorized = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": [],
            "info": [],
            "unknown": []
        }
        
        for dtc_info in dtc_infos:
            severity = dtc_info.severity.lower()
            if severity in categorized:
                categorized[severity].append(dtc_info)
            else:
                categorized["unknown"].append(dtc_info)
        
        return categorized
    
    def get_priority_dtcs(self, dtc_infos: List[DTCInfo]) -> List[DTCInfo]:
        """
        Get DTCs sorted by priority (severity)
        """
        severity_order = {
            "critical": 0,
            "high": 1,
            "medium": 2,
            "low": 3,
            "info": 4,
            "unknown": 5
        }
        
        return sorted(
            dtc_infos,
            key=lambda x: severity_order.get(x.severity.lower(), 5)
        )
    
    def get_related_dtcs(self, dtc_code: str) -> List[str]:
        """
        Get DTCs that are commonly related to the given DTC
        """
        related = []
        
        # Check for common patterns
        if dtc_code.startswith('P030'):  # Misfire codes
            related.extend(['P0300', 'P0301', 'P0302', 'P0303', 'P0304'])
        elif dtc_code.startswith('P017'):  # Lean codes
            related.extend(['P0171', 'P0174'])
        elif dtc_code.startswith('P042'):  # Catalyst codes
            related.extend(['P0420', 'P0430'])
        
        # Remove the original code from related list
        if dtc_code in related:
            related.remove(dtc_code)
        
        return related
    
    def generate_diagnostic_summary(self, dtc_infos: List[DTCInfo]) -> Dict[str, Any]:
        """
        Generate a comprehensive diagnostic summary
        """
        if not dtc_infos:
            return {
                "total_dtcs": 0,
                "status": "No DTCs found",
                "severity_breakdown": {},
                "system_breakdown": {},
                "recommendations": ["Vehicle appears to be operating normally"]
            }
        
        # Count by severity
        severity_counts = {}
        system_counts = {}
        all_causes = []
        all_hints = []
        
        for dtc in dtc_infos:
            # Count severities
            severity = dtc.severity.lower()
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Count systems
            system = dtc.system
            system_counts[system] = system_counts.get(system, 0) + 1
            
            # Collect causes and hints
            all_causes.extend(dtc.possible_causes)
            all_hints.extend(dtc.repair_hints)
        
        # Determine overall status
        if "critical" in severity_counts or "high" in severity_counts:
            status = "Immediate attention required"
        elif "medium" in severity_counts:
            status = "Service recommended"
        else:
            status = "Minor issues detected"
        
        # Generate recommendations
        recommendations = []
        if "critical" in severity_counts or "high" in severity_counts:
            recommendations.append("Stop driving and seek immediate professional service")
        elif "medium" in severity_counts:
            recommendations.append("Schedule service appointment soon")
        
        # Add most common repair hints
        unique_hints = list(set(all_hints))[:5]  # Top 5 unique hints
        recommendations.extend(unique_hints)
        
        return {
            "total_dtcs": len(dtc_infos),
            "status": status,
            "severity_breakdown": severity_counts,
            "system_breakdown": system_counts,
            "recommendations": recommendations,
            "most_common_causes": list(set(all_causes))[:10]  # Top 10 unique causes
        }
