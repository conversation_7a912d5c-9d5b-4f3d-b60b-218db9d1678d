"""
FastAPI main application for OBD2 AI Diagnostic System
"""
import logging
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

from .config import settings
from .api.routes import router as api_router
from .api.models import ErrorResponse


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Add loguru logger
logger.add(
    settings.log_file,
    rotation="10 MB",
    retention="30 days",
    level=settings.log_level
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager
    """
    # Startup
    logger.info("Starting OBD2 AI Diagnostic System")
    
    # Initialize database connections, load models, etc.
    try:
        # Add any startup tasks here
        logger.info("System initialization completed")
    except Exception as e:
        logger.error(f"Startup error: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down OBD2 AI Diagnostic System")
    
    # Cleanup tasks
    try:
        # Add any cleanup tasks here
        logger.info("Cleanup completed")
    except Exception as e:
        logger.error(f"Shutdown error: {e}")


# Create FastAPI application
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description=settings.api_description,
    debug=settings.debug,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    Global exception handler for unhandled errors
    """
    logger.error(f"Unhandled exception: {exc}")
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="An unexpected error occurred",
            details={"exception": str(exc)} if settings.debug else None
        ).dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """
    HTTP exception handler
    """
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"HTTP {exc.status_code}",
            message=exc.detail
        ).dict()
    )


# Include API routes
app.include_router(api_router, prefix="/api/v1", tags=["OBD2 Diagnostics"])


# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint with system information
    """
    return {
        "name": settings.api_title,
        "version": settings.api_version,
        "description": settings.api_description,
        "status": "running",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",  # Would use actual timestamp
        "version": settings.api_version
    }


# API documentation endpoints
@app.get("/api/v1/info")
async def api_info():
    """
    API information endpoint
    """
    return {
        "title": settings.api_title,
        "version": settings.api_version,
        "description": settings.api_description,
        "endpoints": {
            "connection": "/api/v1/connect",
            "scan": "/api/v1/scan",
            "analyze": "/api/v1/analyze",
            "brands": "/api/v1/brands",
            "status": "/api/v1/status"
        },
        "features": [
            "OBD2 diagnostic scanning",
            "CAN bus communication",
            "AI-powered analysis",
            "Brand-specific diagnostics",
            "Cost estimation",
            "Maintenance planning"
        ]
    }


# Static files (if needed for web interface)
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass  # Static directory might not exist


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
